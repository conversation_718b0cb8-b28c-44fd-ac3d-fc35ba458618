import { defineConfig } from 'vite'
import fs from 'fs'
import path from 'path'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'node:path'
import devConfig from './config/devServer'
import federation from "@originjs/vite-plugin-federation";
import topLevelAwait from 'vite-plugin-top-level-await';
import AutoImport from 'unplugin-auto-import/vite'
import viteCompression from 'vite-plugin-compression'
import autopreFixer from 'autoprefixer';
import { fileURLToPath } from 'node:url'

const isProd = process.env.NODE_ENV === 'production'
const host = '0.0.0.0'
const port = 8080
const proxySite = devConfig.proxy['^/ai-manager$'].target

export default defineConfig({
  root: resolve(__dirname, 'src'),
  base: isProd ? '/comi/' : '/',
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '#': fileURLToPath(new URL('./public', import.meta.url)),
    }
  },
  css:{
    // 关键代码
    postcss: {
      plugins: [
        autopreFixer({
            // 自动添加前缀
            overrideBrowserslist: [
                'Android 4.1',
                'iOS 7.1',
                'Chrome > 31',
                'ff > 31',
                'ie >= 8',
                //'last 2 versions', // 所有主流浏览器最近2个版本
            ],
            grid: true,
        }),
      ],
    }
  },
  plugins: [
    vue(),
    viteCompression({
      algorithm: 'gzip',
      ext: '.gz',
      // 包含 png 文件
      filter: /\.(js|css|json|txt|html|ico|svg|png)(\?.*)?$/i,
      threshold: 10240, // 大于10k的文件才压缩
      deleteOriginFile: false, //压缩后是否删除源文件
    }),
    {
      name: 'delete-dist',
      buildStart() {
        const distPath = resolve(__dirname, 'dist')
        if (fs.existsSync(distPath)) {
          fs.rmSync(distPath, { recursive: true, force: true })
        }
      }
    },
    federation({
      name: 'comi-custom-card',
      remotes: {
          remote_app: "/custom-card-package/dist/assets/remoteEntry.js",
      },
      shared: ['vue','@seeyon/seeyon-comi-plugins-library'],
    }),
    topLevelAwait({
      // 每个chunk模块的顶级await promise的导出名称
      promiseExportName: "__tla",
      // 每个chunk模块的顶级await promise的导入名称
      promiseImportName: i => `__tla_${i}`
    }),
    // 自定义插件：复制 public 目录到 dist/public
    {
      name: 'copy-public-to-subdirectory',
      generateBundle() {
        // 在生产环境下，手动复制 public 目录
        if (isProd) {
          const publicDir = path.resolve(__dirname, 'public')
          const outputPublicDir = path.resolve(__dirname, 'dist/comi/app/public')

          // 确保输出目录存在
          if (!fs.existsSync(outputPublicDir)) {
            fs.mkdirSync(outputPublicDir, { recursive: true })
          }

          // 复制 public 目录到 dist/public
          const copyRecursiveSync = (src: string, dest: string) => {
            const exists = fs.existsSync(src)
            const stats = exists && fs.statSync(src)
            const isDirectory = exists && stats && stats.isDirectory()

            if (isDirectory) {
              if (!fs.existsSync(dest)) {
                fs.mkdirSync(dest, { recursive: true })
              }
              fs.readdirSync(src).forEach((childItemName) => {
                copyRecursiveSync(path.join(src, childItemName), path.join(dest, childItemName))
              })
            } else {
              fs.copyFileSync(src, dest)
            }
          }

          if (fs.existsSync(publicDir)) {
            fs.readdirSync(publicDir).forEach((item) => {
              const srcPath = path.join(publicDir, item)
              const destPath = path.join(outputPublicDir, item)
              copyRecursiveSync(srcPath, destPath)
            })
          }
        }
      }
    }
  ],
  build: {
    outDir: resolve(__dirname, 'dist/comi/app'),
    sourcemap: !isProd,
    rollupOptions: {
      input: {
        'feedback-help': resolve(__dirname, `src/pages/feedback-help/feedback-help.html`),  
        'index': resolve(__dirname, `src/pages/index/index.html`),
        'native': resolve(__dirname, `src/pages/native/native.html`),
        'personal-center': resolve(__dirname, `src/pages/personal-center/personal-center.html`),
        'preview': resolve(__dirname, `src/pages/preview/preview.html`),
        'square-search': resolve(__dirname, `src/pages/square-search/square-search.html`),
        'bi-preview': resolve(__dirname, `src/pages/bi-preview/bi-preview.html`),
        'digital-dialogue': resolve(__dirname, `src/pages/digital-dialogue/digital-dialogue.html`),
        'more-todo': resolve(__dirname, `src/pages/more-todo/index.html`),
        'intelligent-assistance': resolve(__dirname, `src/pages/intelligent-assistance/index.html`),
      },
      output: {
        // 扁平化输出结构
        entryFileNames: 'js/[name].[hash].js',
        chunkFileNames: 'js/[name].[hash].js',
        assetFileNames: ({ name }) => {
          const ext = path.extname(name ?? '')
          const extName = ext.substring(1)
          // 图片文件
          if (['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp'].includes(extName)) {
            return `images/[name].[hash]${ext}`
          }
          // CSS 文件
          else if (extName === 'css') {
            return `css/[name].[hash]${ext}`
          }
          // 字体文件
          else if (['ttf', 'woff', 'woff2', 'eot', 'otf'].includes(extName)) {
            return `fonts/[name].[hash]${ext}`
          }
          // 其他资源文件
          return `assets/${extName}/[name].[hash]${ext}`
        }
      }
    }
  },
  server: {
    ...devConfig,
    open: '/pages/index/index.html',
    fs: {
      // 允许访问src目录和node_modules
      allow: [
        path.resolve(__dirname, 'src'),
        path.resolve(__dirname, 'node_modules')
      ],
      strict: false
    },
    host: host,
    port: port,
    proxy: {
      '^/comi/api/.*': {
        target: proxySite,
        changeOrigin: true,
        secure: false,
        headers: {
          origin: new URL(proxySite).origin,
          referer: new URL(proxySite).origin + '/'
        }
      },
      '^/comi/ai-manager/.*': {
        target: proxySite,
        changeOrigin: true,
        secure: false,
        headers: {
          origin: new URL(proxySite).origin,
          referer: new URL(proxySite).origin + '/'
        }
      },
      '/custom-card-package': {
        target: proxySite,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/custom-card-package/, '/custom-card-package')
      },
      '^/node_modules': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: path => [process.cwd(), path].join(''),
      },
      '^/comi/seeyon': {
        target: proxySite,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/comi\/seeyon/, '/comi/seeyon')
      },
      '^/comi/.*': {
        target: `http://${host}:${port}`,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/comi/, ''),
        secure: false,
        ws: true
      },
      '^/seeyon/.*': {
        target: proxySite,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/seeyon/, '/seeyon'),
      }
    }
  }
})