<template>
    <div class="intell-assit-question">
        <div class="title">为你提供智能辅助能力：</div>
        <div @click="handleClick(data)" class="question-list" v-for="item in data" :key="item.id">
            <div class="question-item">
                <img :src="xingxing" class="icon-star" />
                <span class="question-text">{{ item.subject }}</span>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import API from '@/api/index';
import xingxing from '@/assets/images/xingxing.png';
defineOptions({
    name: 'IntellAssitQuestion',
})
const props = defineProps({
    data: {
        type: Array,
        default: () => ([])
    },

})

const _$emit = defineEmits(['click'])

function handleClick(item: any) {
    if (item.isQuestion) {
        API.action.sendMessage(item.subject);
    } else {
        _$emit('click', item)
    }
}
</script>
<style lang="scss" scoped>
.intell-assit-question {
    max-width: 80%;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 0px 12px;

    .title {
        font-family: PingFang SC;
        font-weight: 400;
        font-style: Regular;
        font-size: 16px;
        line-height: 24px;
        color: #00000099;
    }

    .question-item {
        display: inline-block;
        display: flex;
        padding: 0 10px 0 4px;
        height: 34px;
        border-radius: 24px;
        border: 1px solid #D1E0FF;
        align-items: center;
        flex: 1;
        max-width: fit-content;

        .icon-star {
            width: 26px;
            height: 26px;
        }

        .question-text {
            background: linear-gradient(90deg, #AB7DFE 0%, #5873F6 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-family: PingFang SC;
            font-weight: 600;
            font-style: Semibold;
            font-size: 14px;
            line-height: 22px;
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            box-sizing: border-box;
            // max-width: calc(100% - 20px);
            margin-left: 2px;
        }
    }
}
</style>