<template>
  <div class="create-meeting-card">
    <div class="card-header">
      <i class="iconfont ai-icon-shijiananpai create-icon"></i>
      <span class="header-text">发起会议</span>
    </div>
    <div class="card-content">
      <div class="content-item">
        <span class="label">会议主题:</span>
        <span class="value">Ai应用部例行早会</span>
      </div>
      <div class="content-item">
        <span class="label">会议时间:</span>
        <span class="value">2025-07-31 09:00-10:00</span>
         <!-- <DatePicker
          :value="timeStr"
          name="startTime"
          component="nativeDateTimePick"
          @change="handleDateChange"
          @update:value="handleDateInput"
        /> -->
        <span class="iconfont ai-icon-daibanshixiang select-icon"></span>
      </div>
      <div class="content-item">
        <span class="label">会议地点:</span>
        <span class="value">新川1401、北京203</span>
        <span class="iconfont ai-icon-you select-icon"></span>
      </div>
      <div class="content-item">
        <span class="label">与会人:</span>
        <span class="value">
          王维、李昂等12人
        </span>
        <span class="iconfont ai-icon-you select-icon"></span>
      </div>
      <div class="content-item reminder">
        <span class="label">会议提醒</span>
        <label class="toggle-container">
          <input type="checkbox" checked>
          <span class="slider round"></span>
        </label>
      </div>
    </div>
    <div class="card-footer">
      <div v-if="buttonState === 'init'" class="init-button">
        <button class="base-btn cancel-btn" @click="cancelCreate">取消</button>
        <button class="base-btn submit-btn" @click="submitCreate">发起</button>
      </div>
      <button v-if="buttonState === 'submited'" class="base-btn submited-btn">已发起</button>
      <button v-if="buttonState === 'cancelled'" class="base-btn cancelled-btn">已取消</button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import DatePicker from '@/components/date-picker/index.vue';
  const timeStr = ref('2025-07-31 09:00');
  const buttonState = ref('init');
  function cancelCreate() {
    buttonState.value = 'cancelled';
  }
  function submitCreate() {
    buttonState.value = 'submited';
  }

  function handleDateInput(value: string) {
    // 实时输入处理
    console.log('Date input changed:', value);
  };

  function handleDateChange(value: string) {
    // 选择日期处理
    console.log('Date selected:', value);
  };
</script>

<style scoped lang="scss">
.create-meeting-card {
  width: 100%;
  max-width: 500px;
  background: linear-gradient(180deg, #FFFFFF 0%, #E6F0FF 100%);
  border-radius: 10px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  padding: 12px 12px 16px;
  height: 348px;
  box-sizing: border-box;
  background: url('../../assets/images/header-background.png') no-repeat;
  background-size: 100% 100%;
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    height: 22px;
    .create-icon {
      font-size: 18px;
      color: #4379FF;
      margin-right: 4px;
    }
  }
  .calendar-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }
  
  .header-text {
    font-size: 14px;
    font-weight: 600;
    color: rgba(0, 0, 0, 1);
    color: var(--theme-font-color0, rgba(0, 0, 0, 1));
  }
  
  .card-content {
    margin-bottom: 22px;
  }
  
  .content-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    color: #666;
    height: 47px;
    font-size: 14px;
    font-size: var(--theme-font-size1, 14px);
    position: relative;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      display: block;
      height: 1px;
      width: 100%;
      background-color: #d9d9d9;
      transform: scaleY(0.5);
    }
    .select-icon {
      color: rgba(0, 0, 0, 0.4);
      color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
    }
  }
  
  .label {
    margin-right: 8px;
    color: rgba(0, 0, 0, 0.6);
    color: var(--theme-font-color2, rgba(0, 0, 0, 0.6));
  }
  
  .value {
    flex: 1;
    color: rgba(0, 0, 0, 0.9);
    color: var(--theme-font-color1, rgba(0, 0, 0, 0.9));
  }
  
  .user-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
  }
  
  .reminder .toggle-container {
    display: inline-block;
    position: relative;
    width: 44px;
    height: 24px;
  }
  
  .reminder .toggle-container input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
  }
  
  .slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
  }
  
  input:checked + .slider {
    background-color: #4379FF;
  }
  
  input:focus + .slider {
    box-shadow: 0 0 1px #4379FF;
  }
  
  input:checked + .slider:before {
    transform: translateX(20px);
  }
  
  /* Rounded sliders */
  .slider.round {
    border-radius: 34px;
  }
  
  .slider.round:before {
    border-radius: 50%;
  }
  
  .card-footer {
    width: 100%;
    .init-button { 
      display: flex;
      justify-content: space-between;
      gap: 12px;
    }
    .base-btn {
      height: 32px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
    }
    .submited-btn {
      background: linear-gradient(89.79deg, #677EFF 0.31%, #4379FF 53.97%, #6EBBFF 99.07%);
      width: 100%;
      color: #fff;
      opacity: 0.45;
    }
    .cancelled-btn {
      width: 100%;
      border: 1px solid #D8DADF;
      background-color: #fff;
      color: rgba(0, 0, 0, 0.4);
      color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
      opacity: 0.55;
    }
    .cancel-btn, .submit-btn {
      flex: 1;
      cursor: pointer;
    }
    
    .cancel-btn {
      background: #fff;
      color: rgba(0, 0, 0, 0.9);
      color: var(--theme-font-color1, rgba(0, 0, 0, 0.9));
      border: 1px solid #D8DADF;
    }
    
    .submit-btn {
      background: linear-gradient(89.79deg, #677EFF 0.31%, #4379FF 53.97%, #6EBBFF 99.07%);
      color: #fff;
    }
  }
  
}

</style>