<template>
  <div class="scroll-content" :class="{ 'scroll-content-smooth': !state.historyModel }" id="scroll-content" ref="scrollContent">
    <div
      class="history-loading"
      :class="{ 'fade-in': historyService.isLoading, 'fade-out': !historyService.isLoading }"
    >
      <AiStateLoading v-if="historyService.isLoading" />
    </div>

    <IntroducePage
      v-if="!isChat && !isIntellAssit"
      />
    
    <TouchHandler 
      v-else
      :history-params="historyParams"
      :has-more="props.hasMore"
      :on-load-more="loadHistoryData"
      :on-long-press="handleLongPress"
    >
      <MessageListItem
        v-for="content in state.allCardData"
        :content="content"
        :key="content._id"
      />
    </TouchHandler>
    <PendingList v-if="isShowPendingList"></PendingList>
    <div ref="messageBoxBottom" id="messageBoxBottom"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount, inject, onUnmounted } from 'vue';
import API from '@/api/index';
import type { HistoryParams, ChatResponse } from '@/types/chat';
import TouchHandler from './touch-handler.vue';
import MessageListItem from './message-list-item.vue';
import IntroducePage from '@/components/introduce-page/index.vue';
import AiStateLoading from '@/components/ui/ai-state/loading.vue';
import { HistoryService } from '@/api/servers/history-service';
import PendingList from '@/components/pending-list/index.vue';
const props = defineProps({
  content: {
    type: String,
    default: '',
  },
  markdownData: {
    type: Object,
    default: () => ({}),
  },
  isChat: {
    type: Boolean,
    default: false,
  },
  guideData: {
    type: Object,
    default: () => ({}),
  },
  onLoadMore: {
    type: Function,
    default: () => {},
  },
  hasMore: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(['update:hasMore']);

// 是否是智能辅助页面
const isIntellAssit = inject('isIntellAssit') as boolean;

const state = API.store.state;

const historyService = HistoryService.getInstance(state);
const historyParams = computed(() => historyService.params);

/**
 * 加载更多的历史会话数据
 */
const loadHistoryData = async (isLoadMore = true) => {
  const hasMore = await historyService.loadHistoryData(isLoadMore);
  emit('update:hasMore', hasMore);
};

// 是否展示待办列表
const isShowPendingList = computed(() => {
  return !props.isChat && state.tokenStr && !isIntellAssit;
})

/**
 * 处理长按事件
 */
const handleLongPress = (id: string, x: number, y: number) => {
  const targetCard = API.store.card.find(id);
  if (!targetCard) return;
  
  API.action.showTouchAction(targetCard, { x, y });
};

function hideContextMenu(e:any) {
  const targetDom = e.target;
  //如果点击的是按钮，这个时候不由这里销毁
  if(targetDom.closest('.operate-container')) {
    return;
  }
  API.action.closeLongtouch();
}

onMounted(()=>{
  document.addEventListener('click', hideContextMenu, true)
});

onUnmounted(()=>{
  document.removeEventListener('click', hideContextMenu);
});
</script>

<style lang="scss" scoped>
.scroll-content {
  overflow-x: hidden;
  overflow-y: auto;
  height: 100%;
  contain: strict; /* 提高渲染性能 */
  will-change: transform; /* 提示浏览器优化 */
  
  
  .history-loading {
    height: 0;
    display: flex;
    justify-content: center;
    overflow: hidden;
    &.fade-in {
      height: 30px;
      // animation: loadingShow 200ms ease-in-out forwards;
    }
    &.fade-out {
      height:  0;
      // animation: loadingHidden 200ms ease-in-out forwards;
    }
  }
}

.scroll-content-smooth{
  scroll-behavior: smooth;
}

@keyframes loadingShow {
  from { height: 0; }
  to { height: 30px; }
}

@keyframes loadingHidden {
  from { height: 30px; }
  to { height: 0; }
}
</style>