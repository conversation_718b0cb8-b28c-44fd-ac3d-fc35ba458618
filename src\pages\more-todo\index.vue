<template>
  <PendingList class="more-todo" :page-size="30" :show-title="false" :show-recommend-quyestions="false" />
</template>

<script setup lang="ts">
import PendingList from '@/components/pending-list/index.vue';
</script>

<style lang="scss" scoped>
.more-todo {
  background-color: #EDF2FC;
  height: 100%;
  width: 100%;

  :deep(.pending-list) {
    .list-container .todo-item .item-content .item-title {

      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      /* 显示两行 */
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
    }
  }



}
</style>