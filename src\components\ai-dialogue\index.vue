<template>
  <div class="dialogue">
    <div ref="chatContainer" class="ai-chat-container padding-setting">
      <ScrollContent
        :guideData="guideData"
        :isChat="isChat"
        ref="scrollContentConatiner"
      ></ScrollContent>
    </div>
    <ToBottom
      v-show="state.showScrollBtn"
      class="scroll-to-bottom"
      :style="toBottomStyle"
      @toBottom="toBottom"
    />
    <AiFooter v-if="!isApp" />
  </div>
</template>

<script setup lang="ts">
import AiFooter from '@/components/ai-footer/index.vue';
import ScrollContent from '@/components/layout/scroll-content.vue';
import nativeSdk from '@/api/sdk';
import ToBottom from '@/components/to-bottom/index.vue';
import API from '@/api/index';
import { debounce, getDeviceInfo } from '@/utils/common';
import { openWebView } from '@/plugins/app-plugin';
import {
  ref,
  reactive,
  computed,
  onMounted,
  onBeforeUnmount,
  defineExpose
} from 'vue';
import { HistoryService } from '@/api/servers/history-service';
const { isChat } = defineProps({
  isChat: {
    type: Boolean,
    default: false,
  },
});
const state = API.store.state;
const scrollContentConatiner = ref<InstanceType<typeof ScrollContent> | null>(null);
const chatContainer = ref<HTMLElement | null>(null);
let el:HTMLElement;
let chatEl:HTMLElement;
let lastScrollTop = 0;
let isRequesting = false;
let lastScrollTime = 0;
const { isApp } = getDeviceInfo();

let guideData = reactive({
  guideQuestions: [],
  prologue: '',
});

let toBottomStyle = computed(() => {
  const num = state.systemStyle.bottom || 81;
  return {
    bottom: num + 'px',
  };
});


// 中断数据请求
const stopRequest = API.action.interruptAction;

//获取输入内容
nativeSdk.appPlugin.InputPlugin(
  {},
  'registerInput',
  (data:string) => {
    if (data) {
      const inputData = JSON.parse(data);
      let citations:any = [];
      if (inputData.type === 'stop') {
        stopRequest();
      } else if (inputData.type === 'send' && inputData.value) {
        if(inputData.assist) {
          const assist = JSON.parse(inputData.assist);
          API.store.action.setState("selectedAgentInfo", assist);
        }
        if(inputData.file) {
          const fileInfo = JSON.parse(inputData.file);
          citations = getFileInfo(fileInfo);
        }
        API.action.sendMessage(inputData.value, citations);
      } else if (inputData.type === 'touch') {
        API.voice.stop();
      } else if (inputData.type === 'sendCard') {
        API.action.sendCard(inputData.value);
      }
    }
  },
  (err:any) => {
    console.log(err, 'err input');
  }
);

const toBottom = () => {
  if (el && chatEl) {
    el.scrollTop = el.scrollHeight - chatEl.clientHeight;
  }
};

/**
 * 监听原生快捷引导点击事件
 * @param {*} data 
 * @param {*} calllback 
 */
const quickCommands = (data:string, calllback:Function) => {
  let citations:any = [];
  const receiveData = JSON.parse(data);
  const assist = receiveData.assist || null;
  const fileInfo = receiveData.file;
  if(fileInfo) {
    citations = getFileInfo(fileInfo);
  }
  API.store.action.setState("selectedAgentInfo", assist);
  API.action.sendMessage(receiveData?.name, citations);
  calllback();
}

function getFileInfo(fileItem: any) {
  const citations = [];
  const materialSymbolsimeTypeObj: Record<string, string> = {
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'csv': 'text/csv',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'wps': 'application/vnd.wps-office.doc',
    'txt': 'text/plain',
    'et': 'application/vnd.wps-office.et',
    'pdf': 'application/pdf',
    'md': 'text/markdown',
    'markdown': 'text/markdown',
  }
  const fileRequestInfo = {
    fileNumber: fileItem.number,
    fileUrl: fileItem.fileUrl,
    name: fileItem.name,
    size: fileItem.fileSize,
    fileType: materialSymbolsimeTypeObj[fileItem.fileType],
    number: fileItem.number,
  }
  citations.push(fileRequestInfo);
  API.store.action.setStaticData('uploadFileInfo', fileRequestInfo);
  return citations;
}

const stopScrolling = () => {
  API.store.action.setState('isScrolling', false);
}

/**
 * 显示一键到底按钮
 */
const judgeShowScrollBtn = () => {
  API.action.closeLongtouch();
  let scrollBottom = el.scrollHeight - el.scrollTop - el.clientHeight;
  
  if (scrollBottom >= el.clientHeight) {
    API.store.action.setState('showScrollBtn', true);
  } else {
    API.store.action.setState('showScrollBtn', false);
  }
  API.store.action.setState('isScrolling', true);
}

/**
 * 滚动加载更多历史数据
 */
const loadMoreHistoryData = () => {
  if (!el) {
    console.log('el 不存在');
    return;
  }
  
  const scrollTop = el.scrollTop;
  const isScrollingUp = scrollTop < lastScrollTop;
  const currentTime = Date.now();
  const scrollSpeed = Math.abs(scrollTop - lastScrollTop) / (currentTime - lastScrollTime || 1);

  // 只在向上滚动且距离顶部小于200时加载历史数据
  // 并且滚动速度不能太快
  if (isScrollingUp && 
      scrollTop < 200 && 
      !isRequesting && 
      scrollSpeed < 5 &&
      state.historySessionId &&
      !historyService.isInitialLoading) {  // 使用 historyService.isInitialLoading
    // 加载更多历史数据
    isRequesting = true;
    const historyService = HistoryService.getInstance(state);
    if (historyService && state.historyModel && typeof historyService.loadHistoryData === 'function') {
      historyService.loadHistoryData(true).then((hasMore) => {
        // 根据 historyService.isLoading 的变化来触发组件的重新渲染
        if (!historyService.isLoading) {
          state.isScrolling = !state.isScrolling;
        }
      }).catch(err => {
        console.error('历史数据加载失败:', err);
      }).finally(() => {
        isRequesting = false;
      });
    } else {
      isRequesting = false;
    }
  } 
  
  lastScrollTop = scrollTop;
  lastScrollTime = currentTime;
}

// 滚动的防抖处理
const handleToBottomScroll = judgeShowScrollBtn;
// 滚动的历史数据加载防抖处理
const handleLoadMoreScroll = debounce(loadMoreHistoryData, 50);


const openPersonalCenter = () => {
  const params = {
    url: `/pages/personal-center/personal-center.html?openType=normal&type=center`,
    screenOrientation: 'portrait',
    openType: 'normal',
    webviewBg: 'webviewLight',
    webviewBgRgb: "#EDF2FC"
  }
  openWebView(params);
}

const historyService = HistoryService.getInstance(state);

onMounted(() => {
  ZYJSBridge.setEventListener('quickOptClickItem', quickCommands);
  ZYJSBridge.setEventListener('sendCard', API.action.sendCard);
  ZYJSBridge.setEventListener('openPersonalCenter', openPersonalCenter);
  
  if (chatContainer.value) {
    chatEl = chatContainer.value;
    chatEl.addEventListener('touchstart', stopScrolling);
  }
  if (scrollContentConatiner.value) {
    el = scrollContentConatiner.value.$el;
    // 初始化 lastScrollTop
    lastScrollTop = el.scrollTop;
    lastScrollTime = Date.now();
    // 移除旧的监听器（如果存在）
    el.removeEventListener('scroll', handleToBottomScroll);
    el.removeEventListener('scroll', handleLoadMoreScroll);
    // 添加新的监听器
    el.addEventListener('scroll', handleToBottomScroll);
    el.addEventListener('scroll', handleLoadMoreScroll);
  }
});

// 移除监听
onBeforeUnmount(() => {
  ZYJSBridge.removeEventListener('quickOptClickItem', quickCommands);
  ZYJSBridge.removeEventListener('sendCard', API.action.sendCard);
  ZYJSBridge.removeEventListener('openPersonalCenter', openPersonalCenter);
  if (el) {
    el.removeEventListener('scroll', handleToBottomScroll);
    el.removeEventListener('scroll', handleLoadMoreScroll);
  }
  if (chatEl) {
    chatEl.removeEventListener('touchstart', stopScrolling);
  }
});

defineExpose({
  scrollContentConatiner,
  chatContainer,
  toBottom
});
</script>

<style lang="scss" scoped>
.dialogue {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 1px;
  margin-top: 8px;
  .padding-setting {
    padding-left: 12px;
    padding-right: 12px;
  }

  .ai-chat-container {
    height: auto;
    flex: 1;
    overflow-y: auto;
    overflow: hidden;
  }
  .scroll-to-bottom {
    position: absolute;
    right: 18px;
    bottom: 85px;
    z-index: 99;
  }
  > .footer {
    height: auto;
    margin-bottom: 8px;
    flex: 0;
  }
  .ai-request-container {
    margin-bottom: 16px;
  }
}
</style>
