<template>
  <div class="native"></div>
</template>

<script setup lang="ts">
import m3Utils from '@/utils/PortalM3Utils';
import requests from '@/api/requests';
import { closeWebView } from '@/plugins/app-plugin';
import { aiToast } from '@/plugins/ai-toast';
import { getQueryString } from '@/utils/common';
const jumpParams = localStorage.getItem('jumpParams');
// 是否打开m3
const isJump = localStorage.getItem('isJump');
// url参数
const openApp = getQueryString('openApp');
const id = getQueryString('id');
const name = getQueryString('name');
document.title = '';

let params: any = {};

let _$cmp: any = null;

if (jumpParams) {
  params = JSON.parse(jumpParams);
  jumpFn();
} else if (openApp && id && isJump !== 'true') {
  localStorage.setItem('isJump', 'true');
  requests.getTranscriptFiles(id).then(res => {
    if (Number(res.code) === 0 && res.data) {
      const attList = res.data.uploadResults.map((item: any) => {
        return item.atts[0]
      })
      params = {
        id: '',
        name: '新建协同',
        icon: '',
        imgSrc: null,
        parentId: '',
        target: 'newWindow',
        href: '',
        appId: 1,
        openApi: 'openApp',
        params: {
          "type": "comi",
          "option": {
            'openFrom': '',
            "members": "",
            "initFormData": null,
            "subject": name,
            "content": res.data?.guideTxtContent || '',
            "relationId": "",
            "sourceId": "",    // 此处是机器人产生的关联id
            "forwardType": "",
            "templateId": "",
            "attList": attList || [],
          }
        },
        properties: {
          branch: false,
        },
      }
      jumpFn();
    }
  })
} else {
  if (isJump == 'true') {
    localStorage.removeItem('isJump');
    aiToast({
      content: '已成功发起协同，同步会议纪要',
      timer: 1500,
    })
    setTimeout(() => {
      closeWebView('')
    }, 1800)
  } else {
    closeWebView('');
  }
}
function jumpFn() {
  requests.getThirdToken().then(data => {
      if (Number(data.code) === 0 && data.data) {
        localStorage.setItem('CMP_V5_TOKEN', data.data);
        // openWebView(params);
        m3Utils.getApi(params, (apiFun: any) => {
          _$cmp = (window as any).cmp;
          if(params.appId === 62) {
            _$cmp.href.next('/seeyon/m3/apps/m3/search/layout/addressbook-memberDetail.html', {
                memberId: '-8603252206078505744',
                comeFrom: "0"
            });
          }else if(params.appId === 66) { //cap4表单
            jumpToCap4(params.params);
          }else if(params.appId === 127){
          //   const boxTypeMap = {
          //     141: 1,
          //     142: 2,
          //     143: 3,
          //     144: 4
          //   };
          //   let boxType = boxTypeMap[params.appId], mailType = null;
          //   "141" == params.appId ? mailType = 1 : "142" == params.appId && (mailType = 2);
          //   const options = {
          //       renderType: 7,
          //       mailId: params.params.id,
          //       boxType: boxType,
          //       mailType: mailType
          //   };
          //  let url = "/seeyon/m3/apps/v5/internalmail/htmls/index.html";
          //  _$cmp.href.next(url, options)
          }else if(apiFun){
            // setTimeout(()=>{
              apiFun(params.params);

            // }, 5000)
          }
          localStorage.removeItem('jumpPage');
          // 10.1.131.181:80
        });
      }
    });
}
//来自于全文检索移动端的逻辑
function jumpToCap4(params: any) {
  const requestParams = {
    moduleId: params.id
  }
  const _$cmp = (window as any).cmp;

  const url = _$cmp.seeyonbasepath + "/rest/cap4/form/findQueryParam4Index";
  _$cmp.ajax({
      type: "POST",
      url: url,
      dataType: "json",
      contentType: "application/json",
      data: JSON.stringify(requestParams),
      success: function(res:any) {
        let data = res.data, pageUrl = "/seeyon/m3/apps/v5/cap4/htmls/native/form/index.html";
        let obj = {
          formType: "main",
          params: {
              rightId: data.rightId,
              moduleId: data.moduleId,
              moduleType: data.moduleType,
              operateType: data.operateType
          },
          title: "required",
          type: "browse"
        };
        const options = {
          animated: !0,
          nativeBanner: !0,
          openWebViewCatch: !0,
          pushInDetailPad: !0
        };
        _$cmp.href.next(pageUrl, obj, options)
      },
      error: function(res: any){
        if(res.message) {
          aiToast({
            content: res.message,
            callback: ()=>{
              closeWebView('');
            }
          })
        }
      }
  })
}
</script>

<style lang="scss" scoped>
.native {
  box-sizing: border-box;
  height: 100%;
  width: 100%;
}
</style>
<style>
#app {
  height: 100%;
  width: 100%;
}
</style>
