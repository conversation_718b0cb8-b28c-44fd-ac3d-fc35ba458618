<template>
  <div class="ai-data-card">
    <DataName :data="data" v-if="API.store.state.historyModel"></DataName>
    <!-- 图标类卡片展示 -->
    <!-- <BiCard v-if="showBiCard(item)" :content="item.context"></BiCard> -->
    <CardShell :style="{'display': shellStyle}">
      <ThinkProcess  v-if="hasProcessData" :processData="data.processData" class="data-think-item"></ThinkProcess>
      <div v-if="!hasCardData && !data.isHistory && data.processCompleted">
        <AiTextLoading />
      </div>
      <div v-if="hasCardData">
        <div v-for="item in data.cardData" :key="item.index">
          <DataIframe v-if="showIframe(item)" :content="item.context"></DataIframe>
          <AiTextLoading v-if="item.isCard && !item.finish" />
          <ComiMarkdown
            v-if="showMarkdown(item)"
            isAppModel
            :extraClassName="isDigtalChat ? 'stream-app-dark': ''"
            :content="getContent(item)"
            @tagClick="handleTagClick"
            />
          <div v-if="showChart(item)">
            <div v-for="(result, resultIndex) in item.json.data.result" :key="resultIndex">
              <DataTable v-if="result?.renderInfo?.type === 'table'" :content="result?.renderInfo" />
              <DataChart v-else :content="result?.renderInfo" />
            </div>
          </div>
          <BiCard :ref="(el) => setBiCardRef(el, item.index)" v-if="showBiCard(item)" :cardData="item.json"></BiCard>

        </div>
        <div v-if="data.isStop" class="stop-card">已停止回答</div>
        <KnowledgeSource v-if="isShowKnowledge" :knowledgeData="data.knowledgeData"></KnowledgeSource>
        <DataButton
          v-if="isShowButton"
          :rightIconArray="rightIconArray"
          :leftIconArray="leftIconArray"
          @iconClick="iconClick"
          class="button-setting"
        ></DataButton>
      </div>
      <span v-if="(data.isHistory || data.isStop) && !hasCardData" class="interrupt-text">已停止回答</span>
    </CardShell>
    <RecommendQuestions class="questions" v-if="isShowRecommend" :recommendData="data.recommandQuestion" :id="content._id"/>
  </div>
</template>

<script setup lang="ts">
import CardShell from '@/components/card-shell/index.vue';
import DataChart from '../data-chart/index.vue';
import DataTable from '../data-table/index.vue';
import AiTextLoading from '@/components/ui/ai-text-loading/index.vue';
import DataButton from './data-button.vue';
import KnowledgeSource from './knowledge-source.vue';
import DataIframe from './data-iframe.vue';
import DataName from './data-name.vue';
import BiCard from './bi-card.vue';
import { computed, ref, isRef, isReactive, nextTick, onMounted, onUnmounted } from 'vue';
import API from '@/api/index';
import {CardContentType, AiDataCard} from '@/types/api';
import { ComiMarkdown, ThinkProcess } from '@seeyon/seeyon-comi-plugins-library';
import RecommendQuestions from './recommend-questions.vue';
import { HistoryService } from '@/api/servers/history-service';
console.log(ThinkProcess, "ThinkProcessThinkProcessThinkProcess")
interface BiCardRef {
  ref: any;
  index: number;
}

const biCardWapperRef = ref<Record<string, any>>({});

const props = defineProps({
  data: {
    type: Object,
    default: ()=>({}),
  },
  content: {
    type: Object as () => AiDataCard,
    default: ()=>({}),
  }
});
console.log(props.data, "props.data")
const nameMap: Record<string, boolean> = {}
let isIframe = ref(false);
let isShowBlock = ref(false);
const isDigtalChat = API.store.staticData.isDigtalChat;
const historyService = HistoryService.getInstance(API.store.state);
let observer: MutationObserver | null = null;
function setupScrollObserver() {
  const container = document.querySelector('#scroll-content');
  if (!container) return;
  observer = new MutationObserver(() => {
    nextTick(() => {
      API.store.scrollToBottom();
    });
  });

  observer.observe(container, {
    childList: true,
    subtree: true,
    attributes: true,
    characterData: true
  });

  // 监听一段时间后断开
  setTimeout(() => {
    if (observer) {
      observer.disconnect();
      observer = null;
    }
  }, 1000); // 1秒后断开监听
}

if(API.store.state.historyModel){
  if(historyService.isInitialLoading){
    nextTick(() => {
      API.store.action.setState('isScrolling', true);
      API.store.scrollToBottom();
      setupScrollObserver();
    });
  }
}else{
  toBottom();
}

onUnmounted(() => {
  if (observer) {
    observer.disconnect();
    observer = null;
  }
});

let rightIconArray = computed(()=>{
  const rightButton = [];
  const copy = {
    id: 'copy',
    name: 'ai-icon-quanwenfuzhi',
  };
  rightButton.push(copy);
  //点踩需要有messageId
  if(props.data.messageId) {
    let unlike = {
      id: 'unlike',
      name: '',
    }
    if(props.data.isUnlike) {
      unlike.name = 'ai-icon-diancai-mian icon-blue';
    }else {
      unlike.name = 'ai-icon-diancai-xian';
    }
    rightButton.push(unlike);
  }
  return rightButton;
});
let leftIconArray = computed(() =>{
  const readItem = {
    id: 'read',
    name: 'ai-icon-bobao-xianbeifen',
    type: ''
  };
  if(props.data.isPlay) {
    readItem.name = 'reading';
    readItem.type = 'image';
  }
  return [readItem];
})
const hasProcessData = computed(()=>{
  return props.data.processData?.length > 0 && !isDigtalChat;
})
const hasCardData = computed(()=>{
  return props.data?.cardData?.length > 0;
})

const shellStyle = computed(()=>{
  
  return (hasProcessData.value || props.data.systemAgent || isIframe.value || isShowBlock.value ) ? 'block':'inline-block';
})

const getContent = computed(()=>{
  return (item: CardContentType) => ({
    context: item.context,
    finish: item.finish ?? 0,
    tagData: item.tagData
  })
})

//敏感状态不显示
const isShowButton = computed(()=>{
  return props.content.config?.isLast && props.data.isCompleted && !props.data.illageSessionType && !props.data.isStop && !isDigtalChat;
})
const isShowRecommend = computed(()=>{
  return props.data.recommandQuestion?.length && props.content.config?.isLast && props.data.isCompleted && !isDigtalChat;
})
const isShowKnowledge = computed(()=>{
  return props.data.knowledgeData?.length && !isDigtalChat && props.data.isCompleted;
})
function showMarkdown(item: CardContentType) {
  return item.context && !item.isCard && !item.isIframe && !item.isKnowledgeData
}
function showIframe(data:CardContentType) {
  data.isIframe && (isIframe.value = true);
  return data.isIframe;
}

function showChart(item: CardContentType) {
  if(item.json?.data?.cardType === 4 && item.isCard) {
    isShowBlock.value = true;
  }
  return item.json?.data?.cardType === 4 && item.isCard && item.finish === 1;
}
function showBiCard(item: CardContentType) {
  if(item.json?.data?.cardType === 6 && item.isCard) {
    isShowBlock.value = true;
  }
  return item.json?.data?.cardType === 6 && item.isCard && item.finish === 1;
}

function iconClick(item: {id: string, name: string, type?: string}) {
  switch (item.id) {
    case 'copy':
      dealCopyText();
      break;
    case 'read':
      updateReadState();
      break;
    case 'unlike': 
      updateCardUnlike();
      break;
  }
}

function updateReadState() {
  //正在播放就暂停
  if(props.data.isPlay) {
    API.action.cardStopVoice(true);
  }else {
    API.action.playVoice(props.content);
  }
}

function updateCardUnlike() {
  //已经点过不喜欢了，在点就取消  TODO: 此处需要后续增加取消不喜欢的接口对接
  if(props.data.isUnlike) {
    props.data.isUnlike = false;
    // API.store.action.saveCardToHistory(props.content)
  }else { //当前状态不是不喜欢，弹出不喜欢页面
    API.action.feedbackDrawerFn(props.content);
  }
}

function dealCopyText() {
  const params = props.content.staticData?.requestParams;
  if(params) {
    API.action.regenerateAssistant(params);
  }
  
  let textValue = '';
  
  // 遍历所有 BiCard 组件获取内容
  Object.keys(biCardWapperRef.value).forEach(key => {
    const biCard = biCardWapperRef.value[key];
    if (biCard && typeof biCard.getMarkdownContent === 'function') {
      const content = biCard.getMarkdownContent();
      textValue += content + '\n';
    }
  });
  
  // 如果没有 BiCard 内容，则获取普通卡片内容
  if (!textValue) {
    textValue = API.store.card.getCardToText(props.data?.cardData);
  }
  
  API.action.copyText(textValue);
}

function handleTagClick(data: any) {
  const isLast = props.content.config?.isLast;
  const targetData = data.data;
  if(targetData.type.toLowerCase() === 'member' && (!nameMap[targetData.matched_word] && isLast || !isLast)) {
    nameMap[targetData.matched_word] = true;
    API.action.getUserList(targetData)
  }
  if(targetData.type.toLowerCase() === 'confirm' && isLast) {
    API.action.sendMessage(targetData.matched_word);
  }
}

// 添加数据验证函数
function validateCardItem(item: any) {
  if (!item) return false;
  return {
    context: item.context || '',
    finish: item.finish !== undefined ? item.finish : 1
  };
}
function toBottom() {
  nextTick(() => {
    API.store.scrollToBottom();
  });
}

// 添加一个方法来设置 ref
function setBiCardRef(el: any, index: number) {
  if (el) {
    biCardWapperRef.value[`biCardWapperRef${index}`] = el;
  }
}
</script>

<style lang="scss" scoped>
.ai-data-card {
  user-select: none;
  -webkit-user-select: none;
  .stop-card {
    margin-top: 8px;
    color: rgba(0,0,0,0.4);
    color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
    font-size:12px;
  }
  .interrupt-text {
    font-size:16px;
    color: rgba(0, 0, 0, 0.4);
    color: var(--theme-font-color3, rgba(0, 0, 0, 0.4));
  }
  .button-setting {
    margin-top: 8px;
  }
  .questions {
    margin-top: 8px;
  }
}
</style>
