<template>
  <div class="intelligent-assistance-wrapper">
    <IntellAssitQuestion :data="questionsData" />
    <Dialogue :isChat="state.isChat" ref="dialogueRef" />
  </div>
</template>

<script setup lang="ts">
import IntellAssitQuestion from "@/components/intelligent-assistance-question/index.vue";
import Dialogue from "@/components/ai-dialogue/index.vue";
import { AssistantInfoType } from "@/types";
import { reactive, provide, ref } from "vue";
import API from '@/api/index'
import { getQueryString } from "@/utils/common";
// 助手信息
let assistantInfo = reactive<AssistantInfoType>({
  name: "",
  iconUrl: "",
  introduce: "",
  prologue: "",
  guideQuestions: [],
  id: "",
  code: "",
});
let state = API.store.state;

provide('isIntellAssit', true)

const intellAssitType = getQueryString('intellAssitType')

const questionsData = ref([
  {
    subject: '智能预审',
    id: 1,
    isQuestion: true,
    type: 'yushen'
  },
  {
    subject: '相同事项排序显示',
    id: 2,
    isQuestion: false,
    type: 'sort',
  }
])
if (intellAssitType === 'approve') {
  questionsData.value = [
    {
      subject: '查询向对方中石油信息',
      id: 1,
      isQuestion: true,
      type: 'yushen'
    },
    {
      subject: '查询历史合同签约信息',
      id: 2,
      isQuestion: true,
      type: 'yushen'
    },
    {
      subject: '辅助审批建议',
      id: 3,
      isQuestion: true,
      type: 'yushen'
    }
  ]
}

// 提供助手信息
provide("assistantInfo", assistantInfo);
</script>

<style lang="scss" scoped>
.intelligent-assistance-wrapper {
  background-color: #edf2fc;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}
</style>
