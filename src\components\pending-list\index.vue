<template>
  <div>
    <div class="pending-list">
      <div class="list-header" v-if="showTitle">
        <span class="title">优先待办</span>
        <span v-if="total > 3" @click="handleMore()" class="get-more">
          <span class="more-link">更多·{{ total }}</span>
          <span class="iconfont ai-icon-you more-icon"></span>
        </span>
      </div>

      <div class="list-container">
        <div v-for="(item) in todoList" :key="item.affairId" class="todo-item" @click="jumpToDetail(item)">
          <div class="item-content">
            <h4 class="item-title">{{ item.content }}</h4>
            <div class="item-meta">
              <div class="user-info">
                <img :src="item.senderFaceUrl" alt="" class="avatar">
                <span class="username">{{ item.senderName }}</span>
                <span class="role">【{{ item.categoryLabel }}】</span>
              </div>
              <span class="time">{{ item.createTime }}</span>
              <i class="iconfont ai-icon-fujian2 attachment-icon" v-if="item.hasAttachment"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <RecommendQuestions class="recommend-questions" v-if="recommendDatas.length && showRecommendQuyestions"
      :recommend-data="recommendDatas" :show-title="false" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import API from '@/api/index';
import RecommendQuestions from '../ai-data-card/recommend-questions.vue';
import { getRecommendQuestions } from '@/api/staticRequest';
import { jumpToTarget } from '@/utils/handle-knowledge';
import { aiToast } from '@/plugins/ai-toast';
import { openWebView } from '@/plugins/app-plugin';
interface TodoItem {
  affairId?: string;
  content?: string;
  categoryLabel?: string;
  createTime?: string;
  senderName?: string;
  senderFaceUrl?: string;
  hasAttachment?: boolean;
  appId?: string;
  readonly?: string;
}

const props = defineProps({
  pageSize: {
    type: Number,
    default: 3,
  },
  showTitle: {
    type: Boolean,
    default: true,
  },
  showRecommendQuyestions: {
    type: Boolean,
    default: true,
  },
});

const todoList = ref<TodoItem[]>([]);
const total = ref(0);
const recommendDatas = ref([])

initPendingList();
async function classificationAll() {
  try {
    let params;
    const { data } = await API.requests.classificationAll();
    const navData = data?.navData || [];
    const prioritySection = navData[0]
    const portletParams = prioritySection.portletParams;
    if (portletParams) {
      try {
        params = JSON.parse(portletParams);
      } catch (error) {
        params = {};
        console.log('解析portletParams失败:', error);
      }
    }
    return params;
  } catch (error) {
    console.error(error);
  }
}

// 根据分类获取待办数据
function getPendingDataByCategory(pageNumber: number = 1, pageSize: number = 20, params: object) {
  API.requests.pendingDataByCategory(pageNumber, pageSize, params).then(res => {
    console.log(res, 'getPendingDataByCategory');
    if (Number(res.code) === 0 && res.data) {
      todoList.value = res.data.data || [];
      total.value = res.data.total || 0;
    }
  });
};
async function initPendingList() {
  const params = await classificationAll();
  getPendingDataByCategory(1, props.pageSize, params);
}

getRecommendQuestions().then(res => {
  console.log(res, 'getRecommendQuestions');
  if (res && res?.data) {
    recommendDatas.value = res.data;
  }
})

function handleMore() {
  //TODO: 待办列表更多
  const params = {
    type: 'todoList',
    url: '/pages/more-todo/index.html',
    notNeedBaseUrl: true,
    subUrl: '/pages/intelligent-assistance/index.html?intellAssitType=pending'
  }
  openWebView(params);
}

//查看详情
function jumpToDetail(item: TodoItem) {
  let appId = Number(item.appId);
  appId = appId === 29 ? 6 : appId;
  const jumpAppID = [1, 4, 6, 7, 8, 10];//协同、公文、会议、公告、新闻、调查

  const params = {
    ...item,
    id: item.affairId,
    name: item.content,
    appType: appId === 29 ? 6 : appId,
    entityId: item.affairId,
  }
  if (jumpAppID.includes(appId) && item?.readonly !== 'readonly') {
    jumpToTarget(params, true);
  } else {
    aiToast({
      content: 'CoMi APP暂不支持查看该详情，请通过PC端尝试',
    });
  }
}

</script>

<style lang="scss" scoped>
.pending-list {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.65) 0%, rgba(250, 252, 255, 0.65) 100%);
  border-radius: 12px;
  padding: 12px;
  backdrop-filter: blur(2px);
  border: 1px solid;
  border-image-source: linear-gradient(180deg, rgba(255, 255, 255, 0.65) 0%, rgba(236, 242, 252, 0.65) 100%);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 7px;
  font-size: 14px;
  font-size: var(--theme-font-size1, 14px);
  line-height: 22px;
  line-height: var(--theme-line-height1, 22px);
  color: rgba(0, 0, 0, 0.6);
  color: var(--theme-font-color2, rgba(0, 0, 0, 0.6));

  .get-more {
    display: flex;
    align-items: center;
  }

  .more-icon {
    font-size: 14px;
    margin-left: 4px;
  }

  // .title {
  // }

  // .more-link {
  //   font-size: 14px;
  //   font-size: var(--theme-font-size1, 14px);
  //   // color: #4379FF;
  //   cursor: pointer;

  //   // &:hover {
  //   //   color: #40a9ff;
  //   // }
  // }
}

.list-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.todo-item {
  background: #fff;
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
}

.item-content {
  width: 100%;
}

.item-title {
  margin-bottom: 4px;
  font-size: 14px;
  font-size: var(--theme-font-size1, 14px);
  font-weight: 500;
  color: rgba(0, 0, 0, 1);
  color: var(--theme-font-color0, rgba(0, 0, 0, 1));
  line-height: 20px;
  line-height: var(--theme-line-height0, 20px);
  font-family: PingFang SC;

}

.item-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  font-size: var(--theme-font-size0, 12px);
  color: rgba(0, 0, 0, 0.6);
  color: var(--theme-font-color2, rgba(0, 0, 0, 0.6));

  .attachment-icon {
    font-size: 14px;
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 4px;
    // flex: 1;
  }

  .avatar {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    object-fit: cover;
    background: #f0f0f0;
  }
}



.username {
  font-family: PingFang SC;
  font-weight: 400;
  font-style: Regular;
  font-size: 12px;
  line-height: 20px;
  font-size: var(--theme-font-size0, 12px);
  max-width: 82px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
}

// .role {
//   color: #666;
//   font-size: 12px;
// }

// .time {
//   color: #999;
//   font-size: 12px;
//   white-space: nowrap;
// }

.recommend-questions {
  margin-top: 12px;
}
</style>
